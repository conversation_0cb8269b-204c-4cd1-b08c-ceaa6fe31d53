use macroquad::prelude::*;
use crate::vehicle::{Vehicle, VehicleState, Direction, LaneType};
use crate::intersection::{Intersection, INTERSECTION_SIZE, LANE_WIDTH, ROAD_LENGTH};
use crate::statistics::Statistics;

pub const WINDOW_WIDTH: f32 = 1200.0;
pub const WINDOW_HEIGHT: f32 = 800.0;

#[derive(Debug)]
pub struct GraphicsEngine {
    pub camera_offset: Vec2,
    pub zoom: f32,
    pub show_debug_info: bool,
    pub show_waypoints: bool,
    pub show_safety_circles: bool,
}

impl Default for GraphicsEngine {
    fn default() -> Self {
        GraphicsEngine {
            camera_offset: Vec2::new(0.0, 0.0),
            zoom: 1.0,
            show_debug_info: true,
            show_waypoints: false,
            show_safety_circles: false,
        }
    }
}

impl GraphicsEngine {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn render_frame(
        &self,
        intersection: &Intersection,
        statistics: &Statistics,
        show_stats: bool,
    ) {
        clear_background(Color::new(0.1, 0.2, 0.1, 1.0)); // Dark green background

        // Render road infrastructure
        self.render_roads(intersection);
        self.render_intersection(intersection);

        // Render vehicles
        for vehicle in &intersection.vehicles {
            self.render_vehicle(vehicle);
            
            if self.show_safety_circles {
                self.render_safety_circle(vehicle);
            }
            
            if self.show_waypoints {
                self.render_vehicle_waypoints(vehicle);
            }
        }

        // Render UI
        if self.show_debug_info {
            self.render_debug_info(intersection, statistics);
        }

        if show_stats {
            self.render_statistics(statistics);
        }

        self.render_controls_help();
    }

    fn render_roads(&self, intersection: &Intersection) {
        let center = intersection.center;
        let half_intersection = INTERSECTION_SIZE / 2.0;
        
        // Road colors
        let road_color = Color::new(0.3, 0.3, 0.3, 1.0);
        let lane_line_color = Color::new(1.0, 1.0, 0.0, 1.0);

        // North-South road
        let ns_road_width = LANE_WIDTH * 6.0; // 3 lanes each direction
        draw_rectangle(
            center.x - ns_road_width / 2.0,
            center.y - half_intersection - ROAD_LENGTH,
            ns_road_width,
            ROAD_LENGTH * 2.0 + INTERSECTION_SIZE,
            road_color,
        );

        // East-West road
        let ew_road_width = LANE_WIDTH * 6.0; // 3 lanes each direction
        draw_rectangle(
            center.x - half_intersection - ROAD_LENGTH,
            center.y - ew_road_width / 2.0,
            ROAD_LENGTH * 2.0 + INTERSECTION_SIZE,
            ew_road_width,
            road_color,
        );

        // Draw lane markings
        self.render_lane_markings(intersection, lane_line_color);
    }

    fn render_lane_markings(&self, intersection: &Intersection, color: Color) {
        let center = intersection.center;
        let half_intersection = INTERSECTION_SIZE / 2.0;

        // North-South lane markings
        for i in 1..6 {
            let x = center.x - (LANE_WIDTH * 3.0) + (LANE_WIDTH * i as f32);
            
            // North side
            draw_line(
                x, center.y - half_intersection - ROAD_LENGTH,
                x, center.y - half_intersection,
                2.0, color
            );
            
            // South side
            draw_line(
                x, center.y + half_intersection,
                x, center.y + half_intersection + ROAD_LENGTH,
                2.0, color
            );
        }

        // East-West lane markings
        for i in 1..6 {
            let y = center.y - (LANE_WIDTH * 3.0) + (LANE_WIDTH * i as f32);
            
            // West side
            draw_line(
                center.x - half_intersection - ROAD_LENGTH, y,
                center.x - half_intersection, y,
                2.0, color
            );
            
            // East side
            draw_line(
                center.x + half_intersection, y,
                center.x + half_intersection + ROAD_LENGTH, y,
                2.0, color
            );
        }

        // Center dividing lines
        draw_line(
            center.x, center.y - half_intersection - ROAD_LENGTH,
            center.x, center.y - half_intersection,
            3.0, WHITE
        );
        draw_line(
            center.x, center.y + half_intersection,
            center.x, center.y + half_intersection + ROAD_LENGTH,
            3.0, WHITE
        );
        draw_line(
            center.x - half_intersection - ROAD_LENGTH, center.y,
            center.x - half_intersection, center.y,
            3.0, WHITE
        );
        draw_line(
            center.x + half_intersection, center.y,
            center.x + half_intersection + ROAD_LENGTH, center.y,
            3.0, WHITE
        );
    }

    fn render_intersection(&self, intersection: &Intersection) {
        let center = intersection.center;
        let half_size = intersection.size / 2.0;
        
        // Intersection area (slightly lighter than roads)
        draw_rectangle(
            center.x - half_size,
            center.y - half_size,
            intersection.size,
            intersection.size,
            Color::new(0.4, 0.4, 0.4, 1.0),
        );

        // Intersection border
        draw_rectangle_lines(
            center.x - half_size,
            center.y - half_size,
            intersection.size,
            intersection.size,
            3.0,
            WHITE,
        );
    }

    fn render_vehicle(&self, vehicle: &Vehicle) {
        // Vehicle body
        let body_color = match vehicle.state {
            VehicleState::Approaching => vehicle.color,
            VehicleState::InIntersection => Color::new(vehicle.color.r * 1.2, vehicle.color.g * 1.2, vehicle.color.b * 1.2, 1.0),
            VehicleState::Exiting => Color::new(vehicle.color.r * 0.8, vehicle.color.g * 0.8, vehicle.color.b * 0.8, 1.0),
            VehicleState::Completed => GRAY,
        };

        // Draw vehicle as a rotated rectangle
        let half_width = vehicle.size.x / 2.0;
        let half_height = vehicle.size.y / 2.0;

        // Calculate corners of the vehicle
        let cos_rot = vehicle.rotation.cos();
        let sin_rot = vehicle.rotation.sin();

        let corners = [
            Vec2::new(-half_width, -half_height),
            Vec2::new(half_width, -half_height),
            Vec2::new(half_width, half_height),
            Vec2::new(-half_width, half_height),
        ];

        let rotated_corners: Vec<Vec2> = corners.iter().map(|corner| {
            Vec2::new(
                corner.x * cos_rot - corner.y * sin_rot + vehicle.position.x,
                corner.x * sin_rot + corner.y * cos_rot + vehicle.position.y,
            )
        }).collect();

        // Draw vehicle body
        for i in 0..4 {
            let next_i = (i + 1) % 4;
            draw_line(
                rotated_corners[i].x, rotated_corners[i].y,
                rotated_corners[next_i].x, rotated_corners[next_i].y,
                3.0, body_color
            );
        }

        // Fill the vehicle
        draw_circle(vehicle.position.x, vehicle.position.y, half_width.min(half_height), body_color);

        // Draw direction indicator (front of vehicle)
        let front_pos = Vec2::new(
            vehicle.position.x + sin_rot * half_height,
            vehicle.position.y - cos_rot * half_height,
        );
        draw_circle(front_pos.x, front_pos.y, 3.0, WHITE);

        // Draw vehicle ID
        if self.show_debug_info {
            draw_text(
                &vehicle.id.to_string(),
                vehicle.position.x - 10.0,
                vehicle.position.y - vehicle.size.y / 2.0 - 5.0,
                16.0,
                WHITE,
            );
        }

        // Draw braking indicator
        if vehicle.is_braking {
            draw_circle_lines(vehicle.position.x, vehicle.position.y, vehicle.size.x, 2.0, RED);
        }
    }

    fn render_safety_circle(&self, vehicle: &Vehicle) {
        draw_circle_lines(
            vehicle.position.x,
            vehicle.position.y,
            vehicle.safety_distance,
            1.0,
            Color::new(1.0, 0.0, 0.0, 0.3),
        );
    }

    fn render_vehicle_waypoints(&self, vehicle: &Vehicle) {
        for (i, waypoint) in vehicle.waypoints.iter().enumerate() {
            let color = if i == vehicle.current_waypoint {
                YELLOW
            } else if i < vehicle.current_waypoint {
                GREEN
            } else {
                GRAY
            };
            
            draw_circle(waypoint.x, waypoint.y, 5.0, color);
            
            if i > 0 {
                let prev_waypoint = vehicle.waypoints[i - 1];
                draw_line(
                    prev_waypoint.x, prev_waypoint.y,
                    waypoint.x, waypoint.y,
                    1.0, color
                );
            }
        }
    }

    fn render_debug_info(&self, intersection: &Intersection, statistics: &Statistics) {
        let info_text = format!(
            "Vehicles: {} | Density: {:.2} | Avg Speed: {:.1}\nClose Calls: {} | Completed: {}",
            intersection.get_vehicle_count(),
            intersection.get_traffic_density(),
            intersection.get_average_velocity(),
            statistics.close_calls,
            statistics.current_vehicles_passed
        );

        draw_text(&info_text, 10.0, 30.0, 20.0, WHITE);
    }

    fn render_statistics(&self, statistics: &Statistics) {
        let stats_text = statistics.display_summary();
        let lines: Vec<&str> = stats_text.lines().collect();
        
        // Draw background
        let bg_width = 400.0;
        let bg_height = lines.len() as f32 * 25.0 + 20.0;
        let bg_x = (WINDOW_WIDTH - bg_width) / 2.0;
        let bg_y = (WINDOW_HEIGHT - bg_height) / 2.0;
        
        draw_rectangle(bg_x, bg_y, bg_width, bg_height, Color::new(0.0, 0.0, 0.0, 0.8));
        draw_rectangle_lines(bg_x, bg_y, bg_width, bg_height, 2.0, WHITE);

        // Draw text
        for (i, line) in lines.iter().enumerate() {
            draw_text(
                line,
                bg_x + 10.0,
                bg_y + 30.0 + i as f32 * 25.0,
                20.0,
                WHITE,
            );
        }
    }

    fn render_controls_help(&self) {
        let help_text = "Controls: ↑↓←→ Spawn vehicles | R Random mode | ESC Show stats | F1 Toggle debug";
        draw_text(help_text, 10.0, WINDOW_HEIGHT - 30.0, 16.0, LIGHTGRAY);
    }

    pub fn toggle_debug_info(&mut self) {
        self.show_debug_info = !self.show_debug_info;
    }

    pub fn toggle_waypoints(&mut self) {
        self.show_waypoints = !self.show_waypoints;
    }

    pub fn toggle_safety_circles(&mut self) {
        self.show_safety_circles = !self.show_safety_circles;
    }
}
