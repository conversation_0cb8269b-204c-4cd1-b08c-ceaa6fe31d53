{"rustc": 1554108538932869396, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 187265481308423917, "path": 17578502833691920059, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\allocator-api2-6d3836889c94c7ab\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}