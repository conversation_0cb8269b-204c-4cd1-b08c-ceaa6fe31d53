{"rustc": 12013579709055016942, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 2230384901048184464, "deps": [[5230392855116717286, "equivalent", false, 16027929254472578822], [9150530836556604396, "allocator_api2", false, 969325275959923730], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 17767505102954954381]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-d2c2cafac5abc815/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}