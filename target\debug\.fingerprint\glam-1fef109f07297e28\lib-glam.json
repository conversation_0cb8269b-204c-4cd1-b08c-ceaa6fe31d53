{"rustc": 1554108538932869396, "features": "[\"default\", \"scalar-math\", \"std\"]", "declared_features": "[\"approx\", \"bytecheck\", \"bytemuck\", \"core-simd\", \"cuda\", \"debug-glam-assert\", \"default\", \"fast-math\", \"glam-assert\", \"libm\", \"mint\", \"rand\", \"rkyv\", \"scalar-math\", \"serde\", \"std\"]", "target": 10941088099570392219, "profile": 15657897354478470176, "path": 17384398185072390712, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\glam-1fef109f07297e28\\dep-lib-glam", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}