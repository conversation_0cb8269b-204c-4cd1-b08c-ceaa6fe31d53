{"rustc": 1554108538932869396, "features": "[\"hound\", \"lewton\", \"ogg_vorbis\", \"wav\"]", "declared_features": "[\"alac\", \"caf\", \"caf_alac\", \"claxon\", \"default\", \"flac\", \"hound\", \"lewton\", \"ogg_vorbis\", \"wav\"]", "target": 12079521829704514866, "profile": 2241668132362809309, "path": 4957580615846715760, "deps": [[1601764846428912903, "dasp_frame", false, 6472561636946000309], [2900310610916828607, "<PERSON>wton", false, 3076556485863294605], [16696992991567834322, "hound", false, 1609947461622053000], [17999592116325259774, "dasp_sample", false, 1750235433628569184]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\audrey-9d9f3e087810c1c4\\dep-lib-audrey", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}