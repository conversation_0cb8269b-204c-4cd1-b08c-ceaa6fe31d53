{"rustc": 12013579709055016942, "features": "[\"default\", \"scalar-math\", \"std\"]", "declared_features": "[\"approx\", \"bytecheck\", \"bytemuck\", \"core-simd\", \"cuda\", \"debug-glam-assert\", \"default\", \"fast-math\", \"glam-assert\", \"libm\", \"mint\", \"rand\", \"rkyv\", \"scalar-math\", \"serde\", \"std\"]", "target": 10941088099570392219, "profile": 15657897354478470176, "path": 18026269868937947612, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/glam-8e9c8a976fda0581/dep-lib-glam", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}