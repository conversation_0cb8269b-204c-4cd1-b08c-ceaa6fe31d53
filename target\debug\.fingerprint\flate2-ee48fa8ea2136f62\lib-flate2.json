{"rustc": 1554108538932869396, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 1269250054977784396, "deps": [[7312356825837975969, "crc32fast", false, 2605055281363851805], [7636735136738807108, "miniz_oxide", false, 3454232249676634562]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-ee48fa8ea2136f62\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}