use macroquad::prelude::*;
use crate::vehicle::{Direction, LaneType};
use crate::intersection::Intersection;
use crate::graphics::GraphicsEngine;
use crate::statistics::Statistics;
use ::rand::{Rng, thread_rng};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum InputAction {
    SpawnNorth,
    SpawnSouth,
    SpawnEast,
    SpawnWest,
    ToggleRandomMode,
    ShowStatistics,
    ToggleDebugInfo,
    ToggleWaypoints,
    ToggleSafetyCircles,
    Quit,
    None,
}

#[derive(Debug)]
pub struct InputHandler {
    pub random_mode: bool,
    pub random_spawn_timer: f32,
    pub random_spawn_interval: f32,
    pub show_statistics: bool,
    
    // Key state tracking to prevent spam
    pub key_pressed_this_frame: std::collections::HashSet<KeyCode>,
    pub key_was_pressed_last_frame: std::collections::HashSet<KeyCode>,
}

impl Default for InputHandler {
    fn default() -> Self {
        InputHandler {
            random_mode: false,
            random_spawn_timer: 0.0,
            random_spawn_interval: 1.0, // Spawn every 1 second in random mode
            show_statistics: false,
            key_pressed_this_frame: std::collections::HashSet::new(),
            key_was_pressed_last_frame: std::collections::HashSet::new(),
        }
    }
}

impl InputHandler {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn update(&mut self, dt: f32) {
        // Update key state tracking
        self.key_was_pressed_last_frame = self.key_pressed_this_frame.clone();
        self.key_pressed_this_frame.clear();

        // Track currently pressed keys
        let keys_to_check = [
            KeyCode::Up, KeyCode::Down, KeyCode::Left, KeyCode::Right,
            KeyCode::R, KeyCode::Escape, KeyCode::F1, KeyCode::F2, KeyCode::F3,
        ];

        for &key in &keys_to_check {
            if is_key_down(key) {
                self.key_pressed_this_frame.insert(key);
            }
        }

        // Update random spawn timer
        if self.random_mode {
            self.random_spawn_timer += dt;
        }
    }

    pub fn get_input_action(&self) -> InputAction {
        // Check for key presses (only trigger once per press)
        if self.is_key_just_pressed(KeyCode::Up) {
            return InputAction::SpawnNorth;
        }
        if self.is_key_just_pressed(KeyCode::Down) {
            return InputAction::SpawnSouth;
        }
        if self.is_key_just_pressed(KeyCode::Left) {
            return InputAction::SpawnWest;
        }
        if self.is_key_just_pressed(KeyCode::Right) {
            return InputAction::SpawnEast;
        }
        if self.is_key_just_pressed(KeyCode::R) {
            return InputAction::ToggleRandomMode;
        }
        if self.is_key_just_pressed(KeyCode::Escape) {
            return InputAction::ShowStatistics;
        }
        if self.is_key_just_pressed(KeyCode::F1) {
            return InputAction::ToggleDebugInfo;
        }
        if self.is_key_just_pressed(KeyCode::F2) {
            return InputAction::ToggleWaypoints;
        }
        if self.is_key_just_pressed(KeyCode::F3) {
            return InputAction::ToggleSafetyCircles;
        }

        InputAction::None
    }

    fn is_key_just_pressed(&self, key: KeyCode) -> bool {
        self.key_pressed_this_frame.contains(&key) && !self.key_was_pressed_last_frame.contains(&key)
    }

    pub fn handle_input(
        &mut self,
        intersection: &mut Intersection,
        graphics: &mut GraphicsEngine,
        statistics: &mut Statistics,
    ) -> bool {
        let action = self.get_input_action();
        
        match action {
            InputAction::SpawnNorth => {
                if let Some(vehicle_id) = intersection.spawn_vehicle(Direction::South, None) {
                    statistics.update_vehicle_spawned();
                    println!("Spawned vehicle {} going North", vehicle_id);
                }
            }
            InputAction::SpawnSouth => {
                if let Some(vehicle_id) = intersection.spawn_vehicle(Direction::North, None) {
                    statistics.update_vehicle_spawned();
                    println!("Spawned vehicle {} going South", vehicle_id);
                }
            }
            InputAction::SpawnEast => {
                if let Some(vehicle_id) = intersection.spawn_vehicle(Direction::West, None) {
                    statistics.update_vehicle_spawned();
                    println!("Spawned vehicle {} going East", vehicle_id);
                }
            }
            InputAction::SpawnWest => {
                if let Some(vehicle_id) = intersection.spawn_vehicle(Direction::East, None) {
                    statistics.update_vehicle_spawned();
                    println!("Spawned vehicle {} going West", vehicle_id);
                }
            }
            InputAction::ToggleRandomMode => {
                self.random_mode = !self.random_mode;
                self.random_spawn_timer = 0.0;
                println!("Random mode: {}", if self.random_mode { "ON" } else { "OFF" });
            }
            InputAction::ShowStatistics => {
                self.show_statistics = !self.show_statistics;
                if self.show_statistics {
                    println!("\n{}", statistics.display_summary());
                }
            }
            InputAction::ToggleDebugInfo => {
                graphics.toggle_debug_info();
                println!("Debug info: {}", if graphics.show_debug_info { "ON" } else { "OFF" });
            }
            InputAction::ToggleWaypoints => {
                graphics.toggle_waypoints();
                println!("Waypoints: {}", if graphics.show_waypoints { "ON" } else { "OFF" });
            }
            InputAction::ToggleSafetyCircles => {
                graphics.toggle_safety_circles();
                println!("Safety circles: {}", if graphics.show_safety_circles { "ON" } else { "OFF" });
            }
            InputAction::Quit => {
                return false;
            }
            InputAction::None => {}
        }

        true
    }

    pub fn handle_random_spawning(&mut self, intersection: &mut Intersection, statistics: &mut Statistics) {
        if self.random_mode && self.random_spawn_timer >= self.random_spawn_interval {
            if let Some(vehicle_id) = intersection.spawn_random_vehicle() {
                statistics.update_vehicle_spawned();
                self.random_spawn_timer = 0.0;
                
                // Vary the spawn interval slightly for more natural traffic
                let mut rng = thread_rng();
                self.random_spawn_interval = rng.gen_range(0.5..2.0);
            } else {
                // If we couldn't spawn, try again sooner
                self.random_spawn_timer = self.random_spawn_interval - 0.1;
            }
        }
    }

    pub fn reset(&mut self) {
        self.random_mode = false;
        self.random_spawn_timer = 0.0;
        self.show_statistics = false;
        self.key_pressed_this_frame.clear();
        self.key_was_pressed_last_frame.clear();
    }

    pub fn get_status_text(&self) -> String {
        let mut status = Vec::new();
        
        if self.random_mode {
            status.push("RANDOM MODE");
        }
        
        if self.show_statistics {
            status.push("SHOWING STATS");
        }

        if status.is_empty() {
            "MANUAL MODE".to_string()
        } else {
            status.join(" | ")
        }
    }
}
