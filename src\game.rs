use macroquad::prelude::*;
use crate::intersection::Intersection;
use crate::physics::PhysicsEngine;
use crate::graphics::{GraphicsEngine, WINDOW_WIDTH, WINDOW_HEIGHT};
use crate::input::InputHandler;
use crate::statistics::Statistics;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum GameState {
    Running,
    Paused,
    ShowingStats,
    Quit,
}

pub struct Game {
    pub state: GameState,
    pub intersection: Intersection,
    pub physics_engine: PhysicsEngine,
    pub graphics_engine: GraphicsEngine,
    pub input_handler: InputHandler,
    pub statistics: Statistics,
    
    // Timing
    pub delta_time: f32,
    pub total_time: f32,
    pub frame_count: u64,
    
    // Performance monitoring
    pub fps: f32,
    pub fps_timer: f32,
    pub fps_frame_count: u32,
}

impl Game {
    pub fn new() -> Self {
        let intersection_center = Vec2::new(WINDOW_WIDTH / 2.0, WINDOW_HEIGHT / 2.0);
        
        Game {
            state: GameState::Running,
            intersection: Intersection::new(intersection_center),
            physics_engine: PhysicsEngine::new(),
            graphics_engine: GraphicsEngine::new(),
            input_handler: InputHandler::new(),
            statistics: Statistics::new(),
            delta_time: 0.0,
            total_time: 0.0,
            frame_count: 0,
            fps: 0.0,
            fps_timer: 0.0,
            fps_frame_count: 0,
        }
    }

    pub async fn run(&mut self) {
        loop {
            // Calculate delta time
            self.delta_time = get_frame_time();
            self.total_time += self.delta_time;
            self.frame_count += 1;

            // Update FPS counter
            self.update_fps();

            // Handle input
            self.input_handler.update(self.delta_time);
            
            let continue_running = self.input_handler.handle_input(
                &mut self.intersection,
                &mut self.graphics_engine,
                &mut self.statistics,
            );

            if !continue_running {
                break;
            }

            // Handle random vehicle spawning
            self.input_handler.handle_random_spawning(&mut self.intersection, &mut self.statistics);

            // Update game state
            match self.state {
                GameState::Running => {
                    self.update();
                }
                GameState::Paused => {
                    // Game is paused, only handle input
                }
                GameState::ShowingStats => {
                    // Show statistics overlay
                }
                GameState::Quit => {
                    break;
                }
            }

            // Render frame
            self.render();

            next_frame().await;
        }

        // Print final statistics
        println!("\n=== FINAL SIMULATION RESULTS ===");
        println!("{}", self.statistics.display_summary());
    }

    fn update(&mut self) {
        // Update intersection and vehicles
        self.intersection.update(
            self.delta_time,
            &self.physics_engine,
            &mut self.statistics,
        );

        // Optimize traffic flow
        self.intersection.optimize_traffic_flow();

        // Check for game state changes
        if self.input_handler.show_statistics {
            self.state = GameState::ShowingStats;
        } else {
            self.state = GameState::Running;
        }
    }

    fn render(&self) {
        // Render the main game
        self.graphics_engine.render_frame(
            &self.intersection,
            &self.statistics,
            self.input_handler.show_statistics,
        );

        // Render additional UI elements
        self.render_status_bar();
        self.render_performance_info();
    }

    fn render_status_bar(&self) {
        let status_text = format!(
            "Mode: {} | FPS: {:.0} | Time: {:.1}s",
            self.input_handler.get_status_text(),
            self.fps,
            self.total_time
        );

        draw_text(&status_text, 10.0, WINDOW_HEIGHT - 60.0, 16.0, WHITE);

        // Compact statistics
        let compact_stats = self.statistics.get_compact_display();
        draw_text(&compact_stats, 10.0, WINDOW_HEIGHT - 80.0, 16.0, LIGHTGRAY);
    }

    fn render_performance_info(&self) {
        if self.graphics_engine.show_debug_info {
            let perf_text = format!(
                "Frame: {} | Delta: {:.3}ms | Vehicles: {}",
                self.frame_count,
                self.delta_time * 1000.0,
                self.intersection.get_vehicle_count()
            );

            draw_text(&perf_text, WINDOW_WIDTH - 300.0, 30.0, 16.0, YELLOW);
        }
    }

    fn update_fps(&mut self) {
        self.fps_timer += self.delta_time;
        self.fps_frame_count += 1;

        if self.fps_timer >= 1.0 {
            self.fps = self.fps_frame_count as f32 / self.fps_timer;
            self.fps_timer = 0.0;
            self.fps_frame_count = 0;
        }
    }

    pub fn reset(&mut self) {
        let intersection_center = Vec2::new(WINDOW_WIDTH / 2.0, WINDOW_HEIGHT / 2.0);
        
        self.state = GameState::Running;
        self.intersection = Intersection::new(intersection_center);
        self.statistics.reset();
        self.input_handler.reset();
        self.total_time = 0.0;
        self.frame_count = 0;
    }

    pub fn pause(&mut self) {
        self.state = GameState::Paused;
    }

    pub fn resume(&mut self) {
        self.state = GameState::Running;
    }

    pub fn quit(&mut self) {
        self.state = GameState::Quit;
    }

    pub fn get_simulation_summary(&self) -> String {
        format!(
            "Smart Road Traffic Simulation Summary\n\
            =====================================\n\
            Simulation Time: {:.2} seconds\n\
            Total Frames: {}\n\
            Average FPS: {:.1}\n\
            \n\
            {}",
            self.total_time,
            self.frame_count,
            self.frame_count as f32 / self.total_time.max(1.0),
            self.statistics.display_summary()
        )
    }
}
