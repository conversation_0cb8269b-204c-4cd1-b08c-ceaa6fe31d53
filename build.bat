@echo off
echo Smart Road - Build Script
echo ========================

echo Checking Rust installation...
rustc --version
if %errorlevel% neq 0 (
    echo Error: Rust is not installed or not in PATH
    echo Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)

echo.
echo Checking for Visual Studio Build Tools...
where link.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Visual Studio Build Tools not found
    echo.
    echo Please install one of the following:
    echo 1. Visual Studio 2019 or later with C++ build tools
    echo 2. Visual Studio Build Tools 2019 or later
    echo 3. Or use WSL with Ubuntu for Linux development
    echo.
    echo Download from: https://visualstudio.microsoft.com/downloads/
    echo.
    echo Alternatively, you can install MSYS2 and MinGW-w64:
    echo 1. Download MSYS2 from https://www.msys2.org/
    echo 2. Install MinGW-w64: pacman -S mingw-w64-x86_64-gcc
    echo 3. Add MinGW-w64 to your PATH
    echo.
    pause
    exit /b 1
)

echo.
echo Building Smart Road...
cargo build --release
if %errorlevel% neq 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful! You can now run the simulation with:
echo cargo run --release
echo.
pause
