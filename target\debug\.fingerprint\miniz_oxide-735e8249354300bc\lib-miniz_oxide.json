{"rustc": 12013579709055016942, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 15545573834363760220, "deps": [[4018467389006652250, "simd_adler32", false, 8862100339790325936], [7911289239703230891, "adler2", false, 1840285936523692043]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-735e8249354300bc/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}