use macroquad::prelude::*;
use std::time::Instant;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum Direction {
    North,
    South,
    East,
    West,
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub enum LaneType {
    Right,    // r - right turn
    Straight, // s - straight
    Left,     // l - left turn
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum VehicleState {
    Approaching,
    InIntersection,
    Exiting,
    Completed,
}

#[derive(Debug, <PERSON>lone)]
pub struct Vehicle {
    pub id: u32,
    pub position: Vec2,
    pub velocity: f32,
    pub target_velocity: f32,
    pub direction: Direction,
    pub lane_type: LaneType,
    pub rotation: f32,
    pub state: VehicleState,
    pub safety_distance: f32,
    pub size: Vec2,
    pub color: Color,
    
    // Physics tracking
    pub distance_traveled: f32,
    pub intersection_entry_time: Option<Instant>,
    pub intersection_exit_time: Option<Instant>,
    pub total_crossing_time: Option<f32>,
    
    // Path planning
    pub waypoints: Vec<Vec2>,
    pub current_waypoint: usize,
    
    // Collision avoidance
    pub is_braking: bool,
    pub min_safe_velocity: f32,
    pub max_velocity: f32,
}

impl Vehicle {
    pub fn new(
        id: u32,
        spawn_direction: Direction,
        lane_type: LaneType,
        spawn_position: Vec2,
        target_velocity: f32,
    ) -> Self {
        let color = match lane_type {
            LaneType::Right => BLUE,
            LaneType::Straight => GREEN,
            LaneType::Left => RED,
        };

        let rotation = match spawn_direction {
            Direction::North => 0.0,
            Direction::South => std::f32::consts::PI,
            Direction::East => -std::f32::consts::PI / 2.0,
            Direction::West => std::f32::consts::PI / 2.0,
        };

        Vehicle {
            id,
            position: spawn_position,
            velocity: 0.0,
            target_velocity,
            direction: spawn_direction,
            lane_type,
            rotation,
            state: VehicleState::Approaching,
            safety_distance: 50.0,
            size: Vec2::new(20.0, 40.0),
            color,
            distance_traveled: 0.0,
            intersection_entry_time: None,
            intersection_exit_time: None,
            total_crossing_time: None,
            waypoints: Vec::new(),
            current_waypoint: 0,
            is_braking: false,
            min_safe_velocity: 10.0,
            max_velocity: target_velocity,
        }
    }

    pub fn update(&mut self, dt: f32) {
        // Update position based on velocity
        let direction_vector = Vec2::new(self.rotation.sin(), -self.rotation.cos());
        let movement = direction_vector * self.velocity * dt;
        self.position += movement;
        self.distance_traveled += movement.length();
    }

    pub fn get_front_position(&self) -> Vec2 {
        let direction_vector = Vec2::new(self.rotation.sin(), -self.rotation.cos());
        self.position + direction_vector * (self.size.y / 2.0)
    }

    pub fn get_bounds(&self) -> (Vec2, Vec2) {
        let half_size = self.size / 2.0;
        (self.position - half_size, self.position + half_size)
    }

    pub fn distance_to(&self, other: &Vehicle) -> f32 {
        self.position.distance(other.position)
    }

    pub fn is_in_intersection(&self, intersection_bounds: (Vec2, Vec2)) -> bool {
        let (min_bounds, max_bounds) = intersection_bounds;
        self.position.x >= min_bounds.x
            && self.position.x <= max_bounds.x
            && self.position.y >= min_bounds.y
            && self.position.y <= max_bounds.y
    }

    pub fn enter_intersection(&mut self) {
        if self.state == VehicleState::Approaching {
            self.state = VehicleState::InIntersection;
            self.intersection_entry_time = Some(Instant::now());
        }
    }

    pub fn exit_intersection(&mut self) {
        if self.state == VehicleState::InIntersection {
            self.state = VehicleState::Exiting;
            self.intersection_exit_time = Some(Instant::now());
            
            if let (Some(entry), Some(exit)) = (self.intersection_entry_time, self.intersection_exit_time) {
                self.total_crossing_time = Some(exit.duration_since(entry).as_secs_f32());
            }
        }
    }
}
