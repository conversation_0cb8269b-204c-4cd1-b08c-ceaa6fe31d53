{"rustc": 1554108538932869396, "features": "[\"hound\", \"lewton\", \"ogg_vorbis\", \"wav\"]", "declared_features": "[\"alac\", \"caf\", \"caf_alac\", \"claxon\", \"default\", \"flac\", \"hound\", \"lewton\", \"ogg_vorbis\", \"wav\"]", "target": 12079521829704514866, "profile": 15657897354478470176, "path": 4957580615846715760, "deps": [[1601764846428912903, "dasp_frame", false, 3451762992774843604], [2900310610916828607, "<PERSON>wton", false, 5939391033098299693], [16696992991567834322, "hound", false, 17325052297264324978], [17999592116325259774, "dasp_sample", false, 11397826543197620830]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\audrey-1005cee33c8fef7c\\dep-lib-audrey", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}