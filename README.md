# Smart Road - Autonomous Traffic Intersection Simulation

A sophisticated traffic intersection simulation built in Rust featuring autonomous vehicles that navigate through a 4-way intersection without traffic lights using intelligent collision avoidance and traffic flow optimization.

## Features

### 🚗 Vehicle Physics & Behavior
- **Autonomous Vehicles**: All vehicles are self-driving with no human intervention
- **Multiple Speed Levels**: 3 different velocity levels (30, 60, 90 units/sec)
- **Safety Distance**: Vehicles maintain strictly positive safety distances
- **Physics Simulation**: Realistic velocity = distance / time calculations
- **Lane Discipline**: Vehicles follow assigned lanes without changing

### 🛣️ Intersection Design
- **4-Way Cross Intersection**: North, South, East, West directions
- **3 Lanes Per Direction**: Right turn (r), Straight (s), Left turn (l)
- **No Traffic Lights**: Pure algorithm-based traffic management
- **Smart Collision Avoidance**: Dynamic speed control and path optimization

### 🎮 Interactive Controls
- **Arrow Keys**: Generate vehicles from specific directions
  - ↑: South to North vehicles
  - ↓: North to South vehicles  
  - →: West to East vehicles
  - ←: East to West vehicles
- **R Key**: Toggle continuous random vehicle generation
- **ESC Key**: Toggle statistics display
- **F1 Key**: Toggle debug information
- **F2 Key**: Toggle waypoint visualization
- **F3 Key**: Toggle safety circle visualization

### 📊 Comprehensive Statistics
- Maximum/minimum velocities achieved
- Maximum/minimum intersection crossing times
- Close calls (safety distance violations)
- Traffic throughput and efficiency metrics
- Safety ratings and performance analysis

### 🎨 Visual Features
- **Color-Coded Vehicles**: Blue (right), Green (straight), Red (left)
- **Smooth Animations**: Proper vehicle rotation and movement
- **Real-time Visualization**: Live traffic flow and statistics
- **Debug Overlays**: Waypoints, safety circles, and performance data

## Prerequisites

### Windows (MSVC)
1. Install Visual Studio 2019 or later with C++ build tools, OR
2. Install Visual Studio Build Tools 2019 or later with C++ workload

### Windows (GNU)
1. Install MSYS2: https://www.msys2.org/
2. Install MinGW-w64 toolchain:
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-pkg-config
   ```
3. Add MinGW-w64 to your PATH

### Alternative: Use WSL (Windows Subsystem for Linux)
1. Install WSL2 with Ubuntu
2. Install Rust in WSL
3. Install required packages:
   ```bash
   sudo apt update
   sudo apt install build-essential pkg-config
   ```

## Installation & Running

1. **Clone or download the project**
   ```bash
   cd smart-road
   ```

2. **Install Rust dependencies**
   ```bash
   cargo build --release
   ```

3. **Run the simulation**
   ```bash
   cargo run --release
   ```

## How to Use

1. **Start the simulation** - The program will open a window with the intersection
2. **Spawn vehicles** using arrow keys or enable random mode with 'R'
3. **Observe the traffic flow** - vehicles will navigate autonomously
4. **Monitor statistics** - press ESC to view detailed performance metrics
5. **Toggle debug features** - use F1, F2, F3 for additional visualizations

## Architecture

### Core Components

- **Vehicle System** (`src/vehicle.rs`): Vehicle physics, state management, and behavior
- **Intersection Management** (`src/intersection.rs`): Traffic flow optimization and lane management
- **Physics Engine** (`src/physics.rs`): Collision detection, movement calculations, and safety systems
- **Graphics Engine** (`src/graphics.rs`): Rendering, animations, and visual effects
- **Input Handler** (`src/input.rs`): Keyboard controls and user interaction
- **Statistics Tracker** (`src/statistics.rs`): Performance metrics and analysis
- **Game Loop** (`src/game.rs`): Main simulation loop and state management

### Smart Intersection Algorithm

The simulation implements several intelligent traffic management strategies:

1. **Collision Avoidance**: Real-time detection and prevention of vehicle collisions
2. **Dynamic Speed Control**: Adaptive velocity adjustment based on traffic conditions
3. **Priority Management**: Intersection vehicles have priority over approaching vehicles
4. **Traffic Flow Optimization**: Density-based spawn rate and speed adjustments
5. **Safety Distance Enforcement**: Maintains safe following distances between vehicles

## Performance Metrics

The simulation tracks comprehensive statistics including:

- **Throughput**: Vehicles per minute passing through intersection
- **Safety**: Close call frequency and safety ratings
- **Efficiency**: Average speeds vs. maximum possible speeds
- **Timing**: Intersection crossing times and traffic flow rates

## Troubleshooting

### Build Issues
- **MSVC Linker Not Found**: Install Visual Studio Build Tools with C++ workload
- **MinGW Not Found**: Install MSYS2 and MinGW-w64 toolchain
- **Dependency Errors**: Run `cargo clean` then `cargo build`

### Performance Issues
- **Low FPS**: Reduce number of vehicles or disable debug visualizations
- **High Memory Usage**: The simulation automatically removes completed vehicles

## Technical Details

- **Language**: Rust 2021 Edition
- **Graphics**: Macroquad game engine
- **Physics**: Custom physics engine with collision detection
- **Architecture**: Modular component-based design
- **Performance**: Optimized for real-time simulation

## Future Enhancements

- Multiple intersection types (T-junction, roundabout)
- Vehicle types with different behaviors
- Weather and road condition effects
- Machine learning-based traffic optimization
- Network of connected intersections

## License

This project is open source. Feel free to modify and distribute according to your needs.

---

**Smart Road** - Demonstrating the future of autonomous vehicle traffic management through intelligent intersection algorithms.
