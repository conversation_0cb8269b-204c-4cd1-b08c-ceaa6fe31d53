{"rustc": 12013579709055016942, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 10459481310629798056, "deps": [[7312356825837975969, "crc32fast", false, 16520132248391241741], [7636735136738807108, "miniz_oxide", false, 5099650417006444901]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-38a8370239e90135/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}