{"rustc": 1554108538932869396, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 10939501480165834489, "deps": [[5230392855116717286, "equivalent", false, 452271111290617021], [9150530836556604396, "allocator_api2", false, 15808296499353419225], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 744333981488943326]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-92aa94fe5aebc14b\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}