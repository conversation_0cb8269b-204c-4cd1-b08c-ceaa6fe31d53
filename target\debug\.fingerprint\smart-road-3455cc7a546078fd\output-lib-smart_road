{"$message_type":"diagnostic","message":"unused imports: `Direction` and `LaneType`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\graphics.rs","byte_start":71,"byte_end":80,"line_start":2,"line_end":2,"column_start":45,"column_end":54,"is_primary":true,"text":[{"text":"use crate::vehicle::{Vehicle, VehicleState, Direction, LaneType};","highlight_start":45,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\graphics.rs","byte_start":82,"byte_end":90,"line_start":2,"line_end":2,"column_start":56,"column_end":64,"is_primary":true,"text":[{"text":"use crate::vehicle::{Vehicle, VehicleState, Direction, LaneType};","highlight_start":56,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\graphics.rs","byte_start":69,"byte_end":90,"line_start":2,"line_end":2,"column_start":43,"column_end":64,"is_primary":true,"text":[{"text":"use crate::vehicle::{Vehicle, VehicleState, Direction, LaneType};","highlight_start":43,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Direction` and `LaneType`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\graphics.rs:2:45\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::vehicle::{Vehicle, VehicleState, Direction, LaneType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `LaneType`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\input.rs","byte_start":59,"byte_end":67,"line_start":2,"line_end":2,"column_start":33,"column_end":41,"is_primary":true,"text":[{"text":"use crate::vehicle::{Direction, LaneType};","highlight_start":33,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\input.rs","byte_start":57,"byte_end":67,"line_start":2,"line_end":2,"column_start":31,"column_end":41,"is_primary":true,"text":[{"text":"use crate::vehicle::{Direction, LaneType};","highlight_start":31,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\input.rs","byte_start":47,"byte_end":48,"line_start":2,"line_end":2,"column_start":21,"column_end":22,"is_primary":true,"text":[{"text":"use crate::vehicle::{Direction, LaneType};","highlight_start":21,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\input.rs","byte_start":67,"byte_end":68,"line_start":2,"line_end":2,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use crate::vehicle::{Direction, LaneType};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `LaneType`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input.rs:2:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::vehicle::{Direction, LaneType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `was_in_intersection`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":3109,"byte_end":3128,"line_start":92,"line_end":92,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"        let was_in_intersection = vehicle.state == VehicleState::InIntersection;","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":3109,"byte_end":3128,"line_start":92,"line_end":92,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"        let was_in_intersection = vehicle.state == VehicleState::InIntersection;","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":"_was_in_intersection","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `was_in_intersection`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:92:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let was_in_intersection = vehicle.state == VehicleState::InIntersection;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_was_in_intersection`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `dt`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":5240,"byte_end":5242,"line_start":153,"line_end":153,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        dt: f32,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":5240,"byte_end":5242,"line_start":153,"line_end":153,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        dt: f32,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":"_dt","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `dt`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:153:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        dt: f32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_dt`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `distance`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\physics.rs","byte_start":7469,"byte_end":7477,"line_start":213,"line_end":213,"column_start":78,"column_end":86,"is_primary":true,"text":[{"text":"    fn should_vehicle_brake(&self, vehicle_a: &Vehicle, vehicle_b: &Vehicle, distance: f32) -> bool {","highlight_start":78,"highlight_end":86}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\physics.rs","byte_start":7469,"byte_end":7477,"line_start":213,"line_end":213,"column_start":78,"column_end":86,"is_primary":true,"text":[{"text":"    fn should_vehicle_brake(&self, vehicle_a: &Vehicle, vehicle_b: &Vehicle, distance: f32) -> bool {","highlight_start":78,"highlight_end":86}],"label":null,"suggested_replacement":"_distance","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `distance`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\physics.rs:213:78\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mehicle, vehicle_b: &Vehicle, distance: f32) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_distance`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `vehicle_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\input.rs","byte_start":6293,"byte_end":6303,"line_start":180,"line_end":180,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"            if let Some(vehicle_id) = intersection.spawn_random_vehicle() {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\input.rs","byte_start":6293,"byte_end":6303,"line_start":180,"line_end":180,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"            if let Some(vehicle_id) = intersection.spawn_random_vehicle() {","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"_vehicle_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `vehicle_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\input.rs:180:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Some(vehicle_id) = intersection.spawn_random_vehicle() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_vehicle_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"6 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 6 warnings emitted\u001b[0m\n\n"}
