{"rustc": 1554108538932869396, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 14166219718623142490, "path": 2600017403833292378, "deps": [[4018467389006652250, "simd_adler32", false, 10979845956601341683], [7911289239703230891, "adler2", false, 9565736231281059569]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-9e9d9d497c9ba281\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}