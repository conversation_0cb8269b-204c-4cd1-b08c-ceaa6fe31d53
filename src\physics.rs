use macroquad::prelude::*;
use crate::vehicle::{Vehicle, VehicleState};
use crate::intersection::Intersection;
use crate::statistics::Statistics;
use ::rand::{Rng, thread_rng};

pub const VELOCITY_LEVELS: [f32; 3] = [30.0, 60.0, 90.0]; // pixels per second
pub const ACCELERATION: f32 = 50.0; // pixels per second squared
pub const DECELERATION: f32 = 100.0; // pixels per second squared
pub const EMERGENCY_BRAKE_DECELERATION: f32 = 200.0;
pub const SAFETY_MARGIN: f32 = 1.2; // Multiplier for safety distance

#[derive(Debug)]
pub struct PhysicsEngine {
    pub gravity: f32,
    pub friction: f32,
    pub collision_detection_enabled: bool,
}

impl Default for PhysicsEngine {
    fn default() -> Self {
        PhysicsEngine {
            gravity: 0.0, // No gravity for top-down view
            friction: 0.95,
            collision_detection_enabled: true,
        }
    }
}

impl PhysicsEngine {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn update_vehicles(
        &self,
        vehicles: &mut Vec<Vehicle>,
        intersection: &Intersection,
        dt: f32,
        statistics: &mut Statistics,
    ) {
        // First pass: Update vehicle states and positions
        for i in 0..vehicles.len() {
            self.update_vehicle_state(&mut vehicles[i], intersection);
            self.update_vehicle_physics(&mut vehicles[i], dt);
        }

        // Second pass: Collision detection and avoidance
        if self.collision_detection_enabled {
            self.handle_collision_avoidance(vehicles, dt, statistics);
        }

        // Third pass: Path following and waypoint navigation
        for vehicle in vehicles.iter_mut() {
            self.update_vehicle_navigation(vehicle, intersection, dt);
        }

        // Remove completed vehicles
        vehicles.retain(|v| v.state != VehicleState::Completed);
    }

    pub fn update_vehicles_with_data(
        &self,
        vehicles: &mut Vec<Vehicle>,
        lanes: &std::collections::HashMap<(crate::vehicle::Direction, crate::vehicle::LaneType), crate::intersection::Lane>,
        intersection_bounds: (Vec2, Vec2),
        dt: f32,
        statistics: &mut Statistics,
    ) {
        // First pass: Update vehicle states and positions
        for i in 0..vehicles.len() {
            self.update_vehicle_state_with_bounds(&mut vehicles[i], intersection_bounds);
            self.update_vehicle_physics(&mut vehicles[i], dt);
        }

        // Second pass: Collision detection and avoidance
        if self.collision_detection_enabled {
            self.handle_collision_avoidance(vehicles, dt, statistics);
        }

        // Third pass: Path following and waypoint navigation
        for vehicle in vehicles.iter_mut() {
            self.update_vehicle_navigation_with_lanes(vehicle, lanes, dt);
        }

        // Remove completed vehicles
        vehicles.retain(|v| v.state != VehicleState::Completed);
    }

    fn update_vehicle_state(&self, vehicle: &mut Vehicle, intersection: &Intersection) {
        let intersection_bounds = intersection.get_intersection_bounds();
        let was_in_intersection = vehicle.state == VehicleState::InIntersection;
        let is_in_intersection = vehicle.is_in_intersection(intersection_bounds);

        match vehicle.state {
            VehicleState::Approaching => {
                if is_in_intersection {
                    vehicle.enter_intersection();
                }
            }
            VehicleState::InIntersection => {
                if !is_in_intersection {
                    vehicle.exit_intersection();
                }
            }
            VehicleState::Exiting => {
                // Check if vehicle has moved far enough from intersection
                let distance_from_center = vehicle.position.distance(intersection.center);
                if distance_from_center > intersection.size + 100.0 {
                    vehicle.state = VehicleState::Completed;
                }
            }
            VehicleState::Completed => {}
        }
    }

    fn update_vehicle_physics(&self, vehicle: &mut Vehicle, dt: f32) {
        // Calculate target velocity based on current situation
        let mut target_velocity = vehicle.target_velocity;

        // Apply braking if necessary
        if vehicle.is_braking {
            target_velocity = vehicle.min_safe_velocity;
        }

        // Smooth velocity changes
        let velocity_diff = target_velocity - vehicle.velocity;
        let acceleration_rate = if velocity_diff > 0.0 {
            ACCELERATION
        } else if vehicle.is_braking {
            EMERGENCY_BRAKE_DECELERATION
        } else {
            DECELERATION
        };

        let velocity_change = acceleration_rate * dt;
        if velocity_diff.abs() <= velocity_change {
            vehicle.velocity = target_velocity;
        } else {
            vehicle.velocity += velocity_change * velocity_diff.signum();
        }

        // Ensure velocity stays within bounds
        vehicle.velocity = vehicle.velocity.max(0.0).min(vehicle.max_velocity);

        // Update position
        vehicle.update(dt);
    }

    fn handle_collision_avoidance(
        &self,
        vehicles: &mut Vec<Vehicle>,
        dt: f32,
        statistics: &mut Statistics,
    ) {
        let vehicle_count = vehicles.len();
        
        // Reset braking state
        for vehicle in vehicles.iter_mut() {
            vehicle.is_braking = false;
        }

        // Check all pairs of vehicles for potential collisions
        for i in 0..vehicle_count {
            for j in (i + 1)..vehicle_count {
                let (vehicle_a, vehicle_b) = if i < j {
                    let (left, right) = vehicles.split_at_mut(j);
                    (&mut left[i], &mut right[0])
                } else {
                    let (left, right) = vehicles.split_at_mut(i);
                    (&mut right[0], &mut left[j])
                };

                self.check_and_resolve_collision(vehicle_a, vehicle_b, statistics);
            }
        }
    }

    fn check_and_resolve_collision(
        &self,
        vehicle_a: &mut Vehicle,
        vehicle_b: &mut Vehicle,
        statistics: &mut Statistics,
    ) {
        let distance = vehicle_a.distance_to(vehicle_b);
        let combined_safety_distance = (vehicle_a.safety_distance + vehicle_b.safety_distance) * SAFETY_MARGIN;

        if distance < combined_safety_distance {
            // Record close call
            statistics.record_close_call();

            // Determine which vehicle should brake based on various factors
            let should_a_brake = self.should_vehicle_brake(vehicle_a, vehicle_b, distance);
            
            if should_a_brake {
                vehicle_a.is_braking = true;
                vehicle_a.min_safe_velocity = (vehicle_a.velocity * 0.5).max(10.0);
            } else {
                vehicle_b.is_braking = true;
                vehicle_b.min_safe_velocity = (vehicle_b.velocity * 0.5).max(10.0);
            }

            // If vehicles are very close, apply emergency braking to both
            if distance < combined_safety_distance * 0.5 {
                vehicle_a.is_braking = true;
                vehicle_b.is_braking = true;
                vehicle_a.min_safe_velocity = 5.0;
                vehicle_b.min_safe_velocity = 5.0;
            }
        }
    }

    fn should_vehicle_brake(&self, vehicle_a: &Vehicle, vehicle_b: &Vehicle, distance: f32) -> bool {
        // Priority rules for determining which vehicle should brake:
        
        // 1. Vehicle in intersection has priority over approaching vehicle
        if vehicle_a.state == VehicleState::InIntersection && vehicle_b.state == VehicleState::Approaching {
            return false; // vehicle_b should brake
        }
        if vehicle_b.state == VehicleState::InIntersection && vehicle_a.state == VehicleState::Approaching {
            return true; // vehicle_a should brake
        }

        // 2. Faster vehicle should brake (has more stopping power)
        if vehicle_a.velocity > vehicle_b.velocity + 10.0 {
            return true;
        }
        if vehicle_b.velocity > vehicle_a.velocity + 10.0 {
            return false;
        }

        // 3. Vehicle with higher ID brakes (arbitrary but consistent)
        vehicle_a.id > vehicle_b.id
    }

    fn update_vehicle_navigation(&self, vehicle: &mut Vehicle, intersection: &Intersection, dt: f32) {
        // Get the lane for this vehicle
        if let Some(lane) = intersection.lanes.get(&(vehicle.direction, vehicle.lane_type)) {
            // Initialize waypoints if not set
            if vehicle.waypoints.is_empty() {
                vehicle.waypoints = lane.waypoints.clone();
                vehicle.current_waypoint = 0;
            }

            // Navigate to current waypoint
            if vehicle.current_waypoint < vehicle.waypoints.len() {
                let target = vehicle.waypoints[vehicle.current_waypoint];
                let direction_to_target = (target - vehicle.position).normalize();
                
                // Update rotation to face the target
                let target_rotation = direction_to_target.y.atan2(direction_to_target.x) + std::f32::consts::PI / 2.0;
                
                // Smooth rotation
                let rotation_diff = target_rotation - vehicle.rotation;
                let rotation_diff = ((rotation_diff + std::f32::consts::PI) % (2.0 * std::f32::consts::PI)) - std::f32::consts::PI;
                
                let max_rotation_speed = 3.0; // radians per second
                let rotation_change = max_rotation_speed * dt;
                
                if rotation_diff.abs() <= rotation_change {
                    vehicle.rotation = target_rotation;
                } else {
                    vehicle.rotation += rotation_change * rotation_diff.signum();
                }

                // Check if we've reached the current waypoint
                let distance_to_waypoint = vehicle.position.distance(target);
                if distance_to_waypoint < 20.0 {
                    vehicle.current_waypoint += 1;
                }
            }
        }
    }

    pub fn get_random_velocity() -> f32 {
        let mut rng = thread_rng();
        VELOCITY_LEVELS[rng.gen_range(0..VELOCITY_LEVELS.len())]
    }

    pub fn calculate_stopping_distance(velocity: f32) -> f32 {
        // Using kinematic equation: d = v²/(2a)
        if velocity <= 0.0 {
            return 0.0;
        }
        (velocity * velocity) / (2.0 * DECELERATION)
    }

    pub fn calculate_safe_following_distance(velocity: f32) -> f32 {
        // Base safety distance plus velocity-dependent component
        let base_distance = 30.0;
        let velocity_component = velocity * 0.8; // 0.8 seconds of travel time
        base_distance + velocity_component
    }

    fn update_vehicle_state_with_bounds(&self, vehicle: &mut Vehicle, intersection_bounds: (Vec2, Vec2)) {
        let _was_in_intersection = vehicle.state == VehicleState::InIntersection;
        let is_in_intersection = vehicle.is_in_intersection(intersection_bounds);

        match vehicle.state {
            VehicleState::Approaching => {
                if is_in_intersection {
                    vehicle.enter_intersection();
                }
            }
            VehicleState::InIntersection => {
                if !is_in_intersection {
                    vehicle.exit_intersection();
                }
            }
            VehicleState::Exiting => {
                // Check if vehicle has moved far enough from intersection
                let center = (intersection_bounds.0 + intersection_bounds.1) / 2.0;
                let distance_from_center = vehicle.position.distance(center);
                if distance_from_center > 300.0 {
                    vehicle.state = VehicleState::Completed;
                }
            }
            VehicleState::Completed => {}
        }
    }

    fn update_vehicle_navigation_with_lanes(
        &self,
        vehicle: &mut Vehicle,
        lanes: &std::collections::HashMap<(crate::vehicle::Direction, crate::vehicle::LaneType), crate::intersection::Lane>,
        _dt: f32,
    ) {
        // Get the lane for this vehicle
        if let Some(lane) = lanes.get(&(vehicle.direction, vehicle.lane_type)) {
            // Initialize waypoints if not set
            if vehicle.waypoints.is_empty() {
                vehicle.waypoints = lane.waypoints.clone();
                vehicle.current_waypoint = 0;
            }

            // Navigate to current waypoint
            if vehicle.current_waypoint < vehicle.waypoints.len() {
                let target = vehicle.waypoints[vehicle.current_waypoint];
                let distance_to_waypoint = vehicle.position.distance(target);
                if distance_to_waypoint < 20.0 {
                    vehicle.current_waypoint += 1;
                }
            }
        }
    }
}
