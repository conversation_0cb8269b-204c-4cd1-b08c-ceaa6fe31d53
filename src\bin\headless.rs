use smart_road::headless::run_headless_simulation;
use std::env;

fn main() {
    println!("🚗 Smart Road - Headless Traffic Simulation");
    println!("==========================================");
    
    // Get duration from command line args or use default
    let duration = env::args()
        .nth(1)
        .and_then(|s| s.parse().ok())
        .unwrap_or(30); // Default 30 seconds

    println!("Running simulation for {} seconds...\n", duration);
    
    run_headless_simulation(duration);
}
