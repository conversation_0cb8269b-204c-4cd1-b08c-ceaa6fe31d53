use macroquad::prelude::*;
use crate::vehicle::{Vehicle, Direction, LaneType, VehicleState};
use crate::physics::{PhysicsEngine, VELOCITY_LEVELS};
use crate::statistics::Statistics;
use std::collections::HashMap;
use rand::Rng;

pub const INTERSECTION_SIZE: f32 = 200.0;
pub const LANE_WIDTH: f32 = 30.0;
pub const ROAD_LENGTH: f32 = 400.0;

#[derive(Debug, Clone)]
pub struct Lane {
    pub direction: Direction,
    pub lane_type: LaneType,
    pub spawn_point: Vec2,
    pub waypoints: Vec<Vec2>,
}

#[derive(Debug)]
pub struct Intersection {
    pub center: Vec2,
    pub size: f32,
    pub lanes: HashMap<(Direction, LaneType), Lane>,
    pub vehicles: Vec<Vehicle>,
    pub next_vehicle_id: u32,
    
    // Traffic management
    pub vehicle_spawn_cooldown: HashMap<Direction, f32>,
    pub min_spawn_distance: f32,
}

impl Intersection {
    pub fn new(center: Vec2) -> Self {
        let mut intersection = Intersection {
            center,
            size: INTERSECTION_SIZE,
            lanes: HashMap::new(),
            vehicles: Vec::new(),
            next_vehicle_id: 1,
            vehicle_spawn_cooldown: HashMap::new(),
            min_spawn_distance: 80.0,
        };

        intersection.initialize_lanes();
        intersection.initialize_spawn_cooldowns();
        intersection
    }

    fn initialize_lanes(&mut self) {
        let directions = [Direction::North, Direction::South, Direction::East, Direction::West];
        let lane_types = [LaneType::Right, LaneType::Straight, LaneType::Left];

        for &direction in &directions {
            for &lane_type in &lane_types {
                let lane = self.create_lane(direction, lane_type);
                self.lanes.insert((direction, lane_type), lane);
            }
        }
    }

    fn initialize_spawn_cooldowns(&mut self) {
        self.vehicle_spawn_cooldown.insert(Direction::North, 0.0);
        self.vehicle_spawn_cooldown.insert(Direction::South, 0.0);
        self.vehicle_spawn_cooldown.insert(Direction::East, 0.0);
        self.vehicle_spawn_cooldown.insert(Direction::West, 0.0);
    }

    fn create_lane(&self, direction: Direction, lane_type: LaneType) -> Lane {
        let (spawn_point, waypoints) = self.calculate_lane_path(direction, lane_type);
        
        Lane {
            direction,
            lane_type,
            spawn_point,
            waypoints,
        }
    }

    fn calculate_lane_path(&self, direction: Direction, lane_type: LaneType) -> (Vec2, Vec<Vec2>) {
        let half_intersection = self.size / 2.0;
        let lane_offset = match lane_type {
            LaneType::Right => -LANE_WIDTH,
            LaneType::Straight => 0.0,
            LaneType::Left => LANE_WIDTH,
        };

        let (spawn_point, waypoints) = match direction {
            Direction::North => {
                let spawn_x = self.center.x + lane_offset;
                let spawn_y = self.center.y + half_intersection + ROAD_LENGTH;
                let spawn_point = Vec2::new(spawn_x, spawn_y);
                
                let waypoints = match lane_type {
                    LaneType::Straight => vec![
                        Vec2::new(spawn_x, self.center.y + half_intersection),
                        Vec2::new(spawn_x, self.center.y - half_intersection),
                        Vec2::new(spawn_x, self.center.y - half_intersection - ROAD_LENGTH),
                    ],
                    LaneType::Right => vec![
                        Vec2::new(spawn_x, self.center.y + half_intersection),
                        Vec2::new(spawn_x, self.center.y + LANE_WIDTH),
                        Vec2::new(self.center.x + half_intersection, self.center.y + LANE_WIDTH),
                        Vec2::new(self.center.x + half_intersection + ROAD_LENGTH, self.center.y + LANE_WIDTH),
                    ],
                    LaneType::Left => vec![
                        Vec2::new(spawn_x, self.center.y + half_intersection),
                        Vec2::new(spawn_x, self.center.y - LANE_WIDTH),
                        Vec2::new(self.center.x - half_intersection, self.center.y - LANE_WIDTH),
                        Vec2::new(self.center.x - half_intersection - ROAD_LENGTH, self.center.y - LANE_WIDTH),
                    ],
                };
                (spawn_point, waypoints)
            },
            Direction::South => {
                let spawn_x = self.center.x - lane_offset;
                let spawn_y = self.center.y - half_intersection - ROAD_LENGTH;
                let spawn_point = Vec2::new(spawn_x, spawn_y);
                
                let waypoints = match lane_type {
                    LaneType::Straight => vec![
                        Vec2::new(spawn_x, self.center.y - half_intersection),
                        Vec2::new(spawn_x, self.center.y + half_intersection),
                        Vec2::new(spawn_x, self.center.y + half_intersection + ROAD_LENGTH),
                    ],
                    LaneType::Right => vec![
                        Vec2::new(spawn_x, self.center.y - half_intersection),
                        Vec2::new(spawn_x, self.center.y - LANE_WIDTH),
                        Vec2::new(self.center.x - half_intersection, self.center.y - LANE_WIDTH),
                        Vec2::new(self.center.x - half_intersection - ROAD_LENGTH, self.center.y - LANE_WIDTH),
                    ],
                    LaneType::Left => vec![
                        Vec2::new(spawn_x, self.center.y - half_intersection),
                        Vec2::new(spawn_x, self.center.y + LANE_WIDTH),
                        Vec2::new(self.center.x + half_intersection, self.center.y + LANE_WIDTH),
                        Vec2::new(self.center.x + half_intersection + ROAD_LENGTH, self.center.y + LANE_WIDTH),
                    ],
                };
                (spawn_point, waypoints)
            },
            Direction::East => {
                let spawn_x = self.center.x - half_intersection - ROAD_LENGTH;
                let spawn_y = self.center.y + lane_offset;
                let spawn_point = Vec2::new(spawn_x, spawn_y);
                
                let waypoints = match lane_type {
                    LaneType::Straight => vec![
                        Vec2::new(self.center.x - half_intersection, spawn_y),
                        Vec2::new(self.center.x + half_intersection, spawn_y),
                        Vec2::new(self.center.x + half_intersection + ROAD_LENGTH, spawn_y),
                    ],
                    LaneType::Right => vec![
                        Vec2::new(self.center.x - half_intersection, spawn_y),
                        Vec2::new(self.center.x - LANE_WIDTH, spawn_y),
                        Vec2::new(self.center.x - LANE_WIDTH, self.center.y + half_intersection),
                        Vec2::new(self.center.x - LANE_WIDTH, self.center.y + half_intersection + ROAD_LENGTH),
                    ],
                    LaneType::Left => vec![
                        Vec2::new(self.center.x - half_intersection, spawn_y),
                        Vec2::new(self.center.x + LANE_WIDTH, spawn_y),
                        Vec2::new(self.center.x + LANE_WIDTH, self.center.y - half_intersection),
                        Vec2::new(self.center.x + LANE_WIDTH, self.center.y - half_intersection - ROAD_LENGTH),
                    ],
                };
                (spawn_point, waypoints)
            },
            Direction::West => {
                let spawn_x = self.center.x + half_intersection + ROAD_LENGTH;
                let spawn_y = self.center.y - lane_offset;
                let spawn_point = Vec2::new(spawn_x, spawn_y);
                
                let waypoints = match lane_type {
                    LaneType::Straight => vec![
                        Vec2::new(self.center.x + half_intersection, spawn_y),
                        Vec2::new(self.center.x - half_intersection, spawn_y),
                        Vec2::new(self.center.x - half_intersection - ROAD_LENGTH, spawn_y),
                    ],
                    LaneType::Right => vec![
                        Vec2::new(self.center.x + half_intersection, spawn_y),
                        Vec2::new(self.center.x + LANE_WIDTH, spawn_y),
                        Vec2::new(self.center.x + LANE_WIDTH, self.center.y - half_intersection),
                        Vec2::new(self.center.x + LANE_WIDTH, self.center.y - half_intersection - ROAD_LENGTH),
                    ],
                    LaneType::Left => vec![
                        Vec2::new(self.center.x + half_intersection, spawn_y),
                        Vec2::new(self.center.x - LANE_WIDTH, spawn_y),
                        Vec2::new(self.center.x - LANE_WIDTH, self.center.y + half_intersection),
                        Vec2::new(self.center.x - LANE_WIDTH, self.center.y + half_intersection + ROAD_LENGTH),
                    ],
                };
                (spawn_point, waypoints)
            },
        };

        (spawn_point, waypoints)
    }

    pub fn get_intersection_bounds(&self) -> (Vec2, Vec2) {
        let half_size = self.size / 2.0;
        (
            Vec2::new(self.center.x - half_size, self.center.y - half_size),
            Vec2::new(self.center.x + half_size, self.center.y + half_size),
        )
    }

    pub fn can_spawn_vehicle(&self, direction: Direction) -> bool {
        let cooldown = self.vehicle_spawn_cooldown.get(&direction).unwrap_or(&0.0);
        if *cooldown > 0.0 {
            return false;
        }

        // Check if there's enough space to spawn a new vehicle
        for vehicle in &self.vehicles {
            if vehicle.direction == direction {
                let distance_from_spawn = match direction {
                    Direction::North => {
                        let spawn_y = self.center.y + self.size / 2.0 + ROAD_LENGTH;
                        (spawn_y - vehicle.position.y).abs()
                    },
                    Direction::South => {
                        let spawn_y = self.center.y - self.size / 2.0 - ROAD_LENGTH;
                        (vehicle.position.y - spawn_y).abs()
                    },
                    Direction::East => {
                        let spawn_x = self.center.x - self.size / 2.0 - ROAD_LENGTH;
                        (vehicle.position.x - spawn_x).abs()
                    },
                    Direction::West => {
                        let spawn_x = self.center.x + self.size / 2.0 + ROAD_LENGTH;
                        (spawn_x - vehicle.position.x).abs()
                    },
                };

                if distance_from_spawn < self.min_spawn_distance {
                    return false;
                }
            }
        }

        true
    }

    pub fn update_spawn_cooldowns(&mut self, dt: f32) {
        for cooldown in self.vehicle_spawn_cooldown.values_mut() {
            if *cooldown > 0.0 {
                *cooldown -= dt;
                if *cooldown < 0.0 {
                    *cooldown = 0.0;
                }
            }
        }
    }

    pub fn set_spawn_cooldown(&mut self, direction: Direction, cooldown_time: f32) {
        self.vehicle_spawn_cooldown.insert(direction, cooldown_time);
    }

    pub fn spawn_vehicle(&mut self, direction: Direction, lane_type: Option<LaneType>) -> Option<u32> {
        if !self.can_spawn_vehicle(direction) {
            return None;
        }

        // Randomly select lane type if not specified
        let lane_type = lane_type.unwrap_or_else(|| {
            let mut rng = rand::thread_rng();
            match rng.gen_range(0..3) {
                0 => LaneType::Right,
                1 => LaneType::Straight,
                _ => LaneType::Left,
            }
        });

        // Get spawn position from lane
        if let Some(lane) = self.lanes.get(&(direction, lane_type)) {
            let target_velocity = PhysicsEngine::get_random_velocity();
            let mut vehicle = Vehicle::new(
                self.next_vehicle_id,
                direction,
                lane_type,
                lane.spawn_point,
                target_velocity,
            );

            // Set up waypoints
            vehicle.waypoints = lane.waypoints.clone();

            let vehicle_id = vehicle.id;
            self.vehicles.push(vehicle);
            self.next_vehicle_id += 1;

            // Set cooldown to prevent immediate spawning
            self.set_spawn_cooldown(direction, 0.5);

            Some(vehicle_id)
        } else {
            None
        }
    }

    pub fn spawn_random_vehicle(&mut self) -> Option<u32> {
        let mut rng = rand::thread_rng();
        let directions = [Direction::North, Direction::South, Direction::East, Direction::West];
        let direction = directions[rng.gen_range(0..directions.len())];
        self.spawn_vehicle(direction, None)
    }

    pub fn update(&mut self, dt: f32, physics_engine: &PhysicsEngine, statistics: &mut Statistics) {
        // Update spawn cooldowns
        self.update_spawn_cooldowns(dt);

        // Update vehicle physics and AI
        physics_engine.update_vehicles(&mut self.vehicles, self, dt, statistics);

        // Update statistics for completed vehicles
        self.vehicles.retain(|vehicle| {
            if vehicle.state == VehicleState::Completed {
                statistics.update_vehicle_completed(vehicle);
                false
            } else {
                true
            }
        });
    }

    pub fn get_traffic_density(&self) -> f32 {
        let intersection_bounds = self.get_intersection_bounds();
        let vehicles_in_intersection = self.vehicles.iter()
            .filter(|v| v.is_in_intersection(intersection_bounds))
            .count();

        vehicles_in_intersection as f32 / 12.0 // Max expected vehicles in intersection
    }

    pub fn get_average_velocity(&self) -> f32 {
        if self.vehicles.is_empty() {
            return 0.0;
        }

        let total_velocity: f32 = self.vehicles.iter().map(|v| v.velocity).sum();
        total_velocity / self.vehicles.len() as f32
    }

    pub fn optimize_traffic_flow(&mut self) {
        let density = self.get_traffic_density();
        let avg_velocity = self.get_average_velocity();

        // Adaptive traffic management based on current conditions
        if density > 0.7 {
            // High density: reduce spawn rate and encourage slower speeds
            for direction in [Direction::North, Direction::South, Direction::East, Direction::West] {
                let current_cooldown = self.vehicle_spawn_cooldown.get(&direction).unwrap_or(&0.0);
                if *current_cooldown < 1.0 {
                    self.set_spawn_cooldown(direction, 1.5);
                }
            }

            // Reduce target velocities for vehicles approaching intersection
            for vehicle in &mut self.vehicles {
                if vehicle.state == VehicleState::Approaching {
                    let distance_to_intersection = vehicle.position.distance(self.center);
                    if distance_to_intersection < 150.0 {
                        vehicle.target_velocity = vehicle.target_velocity.min(VELOCITY_LEVELS[0]);
                    }
                }
            }
        } else if density < 0.3 && avg_velocity < VELOCITY_LEVELS[1] {
            // Low density: allow faster speeds
            for vehicle in &mut self.vehicles {
                if vehicle.state != VehicleState::InIntersection {
                    vehicle.target_velocity = vehicle.max_velocity;
                }
            }
        }
    }

    pub fn get_vehicle_count(&self) -> usize {
        self.vehicles.len()
    }

    pub fn get_vehicles_by_state(&self, state: VehicleState) -> Vec<&Vehicle> {
        self.vehicles.iter().filter(|v| v.state == state).collect()
    }
}
