use crate::vehicle::Vehicle;

#[derive(Debug, Clone)]
pub struct Statistics {
    pub max_vehicles_passed: u32,
    pub current_vehicles_passed: u32,
    pub max_velocity: f32,
    pub min_velocity: f32,
    pub max_crossing_time: f32,
    pub min_crossing_time: f32,
    pub close_calls: u32,
    pub total_vehicles_spawned: u32,
    
    // Additional tracking
    pub average_velocity: f32,
    pub average_crossing_time: f32,
    pub total_crossing_time: f32,
    pub velocity_sum: f32,
    pub velocity_count: u32,
}

impl Default for Statistics {
    fn default() -> Self {
        Statistics {
            max_vehicles_passed: 0,
            current_vehicles_passed: 0,
            max_velocity: 0.0,
            min_velocity: f32::MAX,
            max_crossing_time: 0.0,
            min_crossing_time: f32::MAX,
            close_calls: 0,
            total_vehicles_spawned: 0,
            average_velocity: 0.0,
            average_crossing_time: 0.0,
            total_crossing_time: 0.0,
            velocity_sum: 0.0,
            velocity_count: 0,
        }
    }
}

impl Statistics {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn update_vehicle_spawned(&mut self) {
        self.total_vehicles_spawned += 1;
    }

    pub fn update_vehicle_completed(&mut self, vehicle: &Vehicle) {
        self.current_vehicles_passed += 1;
        self.max_vehicles_passed = self.max_vehicles_passed.max(self.current_vehicles_passed);

        // Update velocity statistics
        if vehicle.velocity > 0.0 {
            self.max_velocity = self.max_velocity.max(vehicle.velocity);
            self.min_velocity = self.min_velocity.min(vehicle.velocity);
            self.velocity_sum += vehicle.velocity;
            self.velocity_count += 1;
            self.average_velocity = self.velocity_sum / self.velocity_count as f32;
        }

        // Update crossing time statistics
        if let Some(crossing_time) = vehicle.total_crossing_time {
            self.max_crossing_time = self.max_crossing_time.max(crossing_time);
            self.min_crossing_time = self.min_crossing_time.min(crossing_time);
            self.total_crossing_time += crossing_time;
            self.average_crossing_time = self.total_crossing_time / self.current_vehicles_passed as f32;
        }
    }

    pub fn record_close_call(&mut self) {
        self.close_calls += 1;
    }

    pub fn reset(&mut self) {
        *self = Self::default();
    }

    pub fn display_summary(&self) -> String {
        let min_velocity_display = if self.min_velocity == f32::MAX { 0.0 } else { self.min_velocity };
        let min_crossing_time_display = if self.min_crossing_time == f32::MAX { 0.0 } else { self.min_crossing_time };

        format!(
            "=== SIMULATION STATISTICS ===\n\
            Total Vehicles Spawned: {}\n\
            Maximum Vehicles Passed: {}\n\
            Current Vehicles Passed: {}\n\
            \n\
            VELOCITY STATISTICS:\n\
            Maximum Velocity: {:.2} units/sec\n\
            Minimum Velocity: {:.2} units/sec\n\
            Average Velocity: {:.2} units/sec\n\
            \n\
            CROSSING TIME STATISTICS:\n\
            Maximum Crossing Time: {:.2} seconds\n\
            Minimum Crossing Time: {:.2} seconds\n\
            Average Crossing Time: {:.2} seconds\n\
            \n\
            SAFETY STATISTICS:\n\
            Close Calls (Safety Distance Violations): {}\n\
            Safety Rating: {}\n\
            \n\
            EFFICIENCY METRICS:\n\
            Throughput Rate: {:.2} vehicles/min\n\
            Traffic Efficiency: {:.1}%\n\
            =============================",
            self.total_vehicles_spawned,
            self.max_vehicles_passed,
            self.current_vehicles_passed,
            self.max_velocity,
            min_velocity_display,
            self.average_velocity,
            self.max_crossing_time,
            min_crossing_time_display,
            self.average_crossing_time,
            self.close_calls,
            self.get_safety_rating(),
            self.get_throughput_rate(),
            self.get_efficiency_percentage()
        )
    }

    pub fn get_safety_rating(&self) -> String {
        let close_call_ratio = if self.current_vehicles_passed > 0 {
            self.close_calls as f32 / self.current_vehicles_passed as f32
        } else {
            0.0
        };

        match close_call_ratio {
            r if r == 0.0 => "EXCELLENT".to_string(),
            r if r < 0.1 => "GOOD".to_string(),
            r if r < 0.3 => "FAIR".to_string(),
            r if r < 0.5 => "POOR".to_string(),
            _ => "DANGEROUS".to_string(),
        }
    }

    pub fn get_throughput_rate(&self) -> f32 {
        // Vehicles per minute (assuming simulation has been running)
        if self.total_crossing_time > 0.0 {
            (self.current_vehicles_passed as f32 / self.total_crossing_time) * 60.0
        } else {
            0.0
        }
    }

    pub fn get_efficiency_percentage(&self) -> f32 {
        // Efficiency based on average velocity vs maximum possible velocity
        if self.average_velocity > 0.0 {
            (self.average_velocity / 90.0) * 100.0 // 90 is max velocity from VELOCITY_LEVELS
        } else {
            0.0
        }
    }

    pub fn get_compact_display(&self) -> String {
        format!(
            "Spawned: {} | Passed: {} | Speed: {:.1} | Close Calls: {} | Safety: {}",
            self.total_vehicles_spawned,
            self.current_vehicles_passed,
            self.average_velocity,
            self.close_calls,
            self.get_safety_rating()
        )
    }
}
