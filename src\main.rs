use macroquad::prelude::*;
use smart_road::game::Game;
use smart_road::graphics::{WINDOW_WIDTH, WINDOW_HEIGHT};

fn window_conf() -> Conf {
    Conf {
        window_title: "Smart Road - Autonomous Traffic Intersection Simulation".to_owned(),
        window_width: WINDOW_WIDTH as i32,
        window_height: WINDOW_HEIGHT as i32,
        window_resizable: false,
        ..Default::default()
    }
}

#[macroquad::main(window_conf)]
async fn main() {
    println!("=== Smart Road Traffic Simulation ===");
    println!("Starting autonomous vehicle intersection simulation...");
    println!();
    println!("Controls:");
    println!("  ↑ Arrow Key: Spawn vehicle going North (from South)");
    println!("  ↓ Arrow Key: Spawn vehicle going South (from North)");
    println!("  → Arrow Key: Spawn vehicle going East (from West)");
    println!("  ← Arrow Key: Spawn vehicle going West (from East)");
    println!("  R Key: Toggle random vehicle generation");
    println!("  ESC Key: Toggle statistics display");
    println!("  F1 Key: Toggle debug information");
    println!("  F2 Key: Toggle waypoint visualization");
    println!("  F3 Key: Toggle safety circle visualization");
    println!();
    println!("Vehicle Colors:");
    println!("  Blue: Right turn vehicles");
    println!("  Green: Straight-through vehicles");
    println!("  Red: Left turn vehicles");
    println!();
    println!("Starting simulation...");

    let mut game = Game::new();
    game.run().await;

    println!("Simulation ended. Thank you for using Smart Road!");
}
