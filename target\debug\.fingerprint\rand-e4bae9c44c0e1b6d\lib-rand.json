{"rustc": 1554108538932869396, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 3373666769829162418, "deps": [[1573238666360410412, "rand_chacha", false, 12195223579060211457], [18130209639506977569, "rand_core", false, 3440268462973922209]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-e4bae9c44c0e1b6d\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}