{"rustc": 12013579709055016942, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 8777892936122626375, "deps": [[1573238666360410412, "rand_chacha", false, 7402663058996336546], [6366008408347001515, "libc", false, 8824080709237791317], [18130209639506977569, "rand_core", false, 2691017623895557469]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-6b02fbed61b788ca/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}