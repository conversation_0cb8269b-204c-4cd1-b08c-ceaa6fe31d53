// Headless version for testing without graphics
use crate::*;
use std::time::{Duration, Instant};

pub struct HeadlessSimulation {
    pub intersection: Intersection,
    pub physics_engine: PhysicsEngine,
    pub statistics: Statistics,
    pub start_time: Instant,
    pub max_duration: Duration,
}

impl HeadlessSimulation {
    pub fn new(max_duration_seconds: u64) -> Self {
        let intersection_center = macroquad::prelude::Vec2::new(600.0, 400.0);
        
        HeadlessSimulation {
            intersection: Intersection::new(intersection_center),
            physics_engine: PhysicsEngine::new(),
            statistics: Statistics::new(),
            start_time: Instant::now(),
            max_duration: Duration::from_secs(max_duration_seconds),
        }
    }

    pub fn run(&mut self) {
        println!("🚗 Starting Smart Road Headless Simulation");
        println!("⏱️  Duration: {} seconds", self.max_duration.as_secs());
        println!("🎯 Spawning vehicles automatically...\n");

        let mut last_spawn = Instant::now();
        let spawn_interval = Duration::from_millis(500); // Spawn every 500ms

        while self.start_time.elapsed() < self.max_duration {
            let dt = 0.016; // ~60 FPS

            // Auto-spawn vehicles
            if last_spawn.elapsed() >= spawn_interval {
                if let Some(vehicle_id) = self.intersection.spawn_random_vehicle() {
                    self.statistics.update_vehicle_spawned();
                    println!("🚙 Spawned vehicle #{}", vehicle_id);
                }
                last_spawn = Instant::now();
            }

            // Update simulation
            self.intersection.update(dt, &self.physics_engine, &mut self.statistics);

            // Print status every 5 seconds
            if self.start_time.elapsed().as_secs() % 5 == 0 {
                self.print_status();
                std::thread::sleep(Duration::from_millis(100)); // Prevent spam
            }

            std::thread::sleep(Duration::from_millis(16)); // ~60 FPS
        }

        self.print_final_results();
    }

    fn print_status(&self) {
        let elapsed = self.start_time.elapsed().as_secs();
        println!("⏰ Time: {}s | Vehicles: {} | Passed: {} | Close Calls: {}", 
                elapsed,
                self.intersection.get_vehicle_count(),
                self.statistics.current_vehicles_passed,
                self.statistics.close_calls);
    }

    fn print_final_results(&self) {
        println!("\n🏁 Simulation Complete!");
        println!("{}", self.statistics.display_summary());
    }
}

// Function to run headless simulation
pub fn run_headless_simulation(duration_seconds: u64) {
    let mut sim = HeadlessSimulation::new(duration_seconds);
    sim.run();
}
