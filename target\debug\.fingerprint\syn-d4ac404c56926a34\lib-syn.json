{"rustc": 12013579709055016942, "features": "[\"clone-impls\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16034882520870291320, "deps": [[373107762698212489, "proc_macro2", false, 736970723967195321], [10637008577242657367, "unicode_ident", false, 1543229022676144193], [17990358020177143287, "quote", false, 11070528316362342222]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-d4ac404c56926a34/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}