{"rustc": 1554108538932869396, "features": "[\"clone-impls\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 12629160231906563322, "deps": [[373107762698212489, "proc_macro2", false, 7541914017210925447], [10637008577242657367, "unicode_ident", false, 6536336232569878840], [17990358020177143287, "quote", false, 5883598920372258485]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-e5b9edf314a4eb7c\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}